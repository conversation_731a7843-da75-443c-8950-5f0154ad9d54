# Comprehensive TLD Support for WebSearchTLDLeadsJob

## Overview

The `WebSearchTLDLeadsJob` now includes comprehensive support for both country code TLDs and generic TLDs based on actual Shopify store usage data.

## TLD Categories Supported

### 1. Country Code TLDs (from API helpers)
These are automatically included from the search helper SUPPORTED_COUNTRY_CODES:
- **Popular**: `.dk`, `.se`, `.no`, `.gb`, `.us`, `.ca`, `.de`, `.au`, `.fr`, `.it`, `.nl`, `.es`, `.br`, `.za`
- **European**: `.at`, `.be`, `.ch`, `.cz`, `.fi`, `.hu`, `.pl`, `.pt`, `.sk`, `.si`, `.bg`, `.hr`, `.ee`, `.lv`, `.lt`, `.mt`, `.cy`, `.lu`
- **Asia-Pacific**: `.in`, `.nz`, `.pk`, `.ph`, `.sg`, `.my`, `.hk`, `.tw`, `.kr`, `.jp`, `.cn`, `.th`, `.id`, `.vn`
- **Americas**: `.mx`, `.cl`, `.pe`, `.ar`, `.uy`, `.ec`, `.gt`, `.pa`, `.cr`, `.ni`, `.sv`, `.hn`, `.bz`, `.do`
- **Middle East & Africa**: `.ae`, `.il`, `.qa`, `.kw`, `.sa`, `.bh`, `.om`, `.eg`, `.ma`, `.tn`, `.ke`, `.na`, `.mu`, `.mg`
- **Others**: `.is`, `.tr`, `.gr`, `.rs`, `.mk`, `.al`, `.me`, `.ba`, `.md`, `.ge`, `.am`, `.az`, `.kz`, `.uz`, `.tm`, `.kg`, `.tj`

### 2. Popular Generic TLDs (High Volume)
Based on your usage data showing high numbers:
- `.com` (4910 stores)
- `.net` (227 stores)
- `.org` (75 stores)
- `.info` (11 stores)
- `.biz` (4 stores)
- `.eu` (143 stores)

### 3. E-commerce & Business TLDs
Specifically relevant for online stores:
- `.store` (425 stores)
- `.shop` (39 stores)
- `.online` (68 stores)
- `.site` (11 stores)
- `.website` (2 stores)
- `.company` (1 store)

### 4. Technology TLDs
Popular in tech and startup communities:
- `.tech` (5 stores)
- `.app` (2 stores)
- `.io` (2 stores)
- `.xyz` (1 store)
- `.co` (634 stores)

### 5. Lifestyle & Specialty TLDs
Niche TLDs for specific industries:
- `.jewelry` (4 stores)
- `.bike` (2 stores)
- `.art` (2 stores)
- `.beer` (2 stores)
- `.clothing` (2 stores)
- `.studio` (2 stores)
- `.pet` (2 stores)
- `.fashion` (1 store)
- `.beauty` (1 store)
- `.health` (1 store)
- `.life` (3 stores)
- `.care` (1 store)
- `.baby` (1 store)
- `.watch` (1 store)
- `.media` (1 store)
- `.school` (1 store)
- `.audio` (1 store)
- `.trade` (1 store)
- `.pro` (1 store)
- `.club` (1 store)
- `.top` (1 store)
- `.faith` (1 store)
- `.direct` (1 store)
- `.fit` (1 store)
- `.cool` (1 store)
- `.dog` (1 store)
- `.cards` (1 store)
- `.global` (1 store)
- `.world` (1 store)
- `.run` (1 store)
- `.one` (2 stores)
- `.shopping` (2 stores)

### 6. Geographic & Cultural TLDs
Location-specific TLDs:
- `.barcelona` (1 store)
- `.amsterdam` (1 store)
- `.africa` (1 store)

### 7. Other Business TLDs
Additional business-focused TLDs:
- `.lighting` (1 store)
- `.eco` (1 store)
- `.tv` (3 stores)
- `.me` (21 stores)

## Search Implementation

### Query Format
Each TLD is searched using the format:
```
site:.{tld} inurl:"/collections/all/" -site:shopify.com -site:stackoverflow.com -site:help.shopify.com -site:accounts.shopify.com -site:shopify.dev -site:myshopify.com
```

### API Handling
- **Country Code TLDs** (2 letters): Use the TLD itself for Google domain selection (e.g., `.dk` → `gl=dk`)
- **Generic TLDs**: Use `us` as default country code for API calls while searching the specific TLD

### Example Searches
```
site:.store inurl:"/collections/all/" -site:shopify.com...
site:.jewelry inurl:"/collections/all/" -site:shopify.com...
site:.bike inurl:"/collections/all/" -site:shopify.com...
site:.barcelona inurl:"/collections/all/" -site:shopify.com...
```

## Usage

### Basic Usage
```php
// Searches ALL supported TLDs (country codes + generic)
WebSearchTLDLeadsJob::dispatch();
```

### Expected Log Output
```
[WebSearchTLDLeadsJob] Processing bing search for TLD: store with query: site:.store inurl:"/collections/all/"...
[WebSearchTLDLeadsJob] Processing bing search for TLD: jewelry with query: site:.jewelry inurl:"/collections/all/"...
[WebSearchTLDLeadsJob] Processing bing search for TLD: dk with query: site:.dk inurl:"/collections/all/"...
```

## Coverage Statistics

Based on your data, this comprehensive TLD list covers:
- **Country Code TLDs**: ~50+ supported countries
- **Generic TLDs**: ~60+ specialty and business TLDs
- **Total Coverage**: Searches across 100+ different TLD extensions
- **Store Coverage**: Covers all TLD types found in your Shopify store analysis

## Benefits

1. **Maximum Coverage**: Searches across all major TLD categories
2. **Industry-Specific**: Includes specialty TLDs like `.jewelry`, `.bike`, `.fashion`
3. **Geographic Diversity**: Covers country codes and city-specific TLDs
4. **Future-Proof**: Includes modern TLDs like `.app`, `.tech`, `.store`
5. **Data-Driven**: Based on actual usage patterns from your store analysis

The job will now discover Shopify stores across the entire spectrum of TLD extensions, significantly expanding your lead generation coverage!
