# WebSearchCountryCodeLeadsJob Improvements

## Overview

This document outlines the improvements made to the `WebSearchCountryCodeLeadsJob` to address the issues identified in the Laravel log analysis.

## Issues Fixed

### 1. Invalid Country Codes ✅
**Problem**: "COM" and "EU" were being processed as country codes, causing API errors.
- `COM` is not a valid ISO country code (it's a TLD)
- `EU` is not a valid ISO 3166-1 alpha-2 country code

**Solution**:
- Removed "COM" and "EU" from the `commonTlds` array
- Added comprehensive country code validation with a blacklist of known invalid codes
- Added logging for filtered invalid codes

### 2. Timeout Errors ✅
**Problem**: Countries like CL, CN, GB were experiencing 30-second timeouts with no retry logic.

**Solution**:
- Implemented retry logic with exponential backoff
- Added configurable timeout settings (default: 60 seconds, minimum: 30 seconds)
- Added intelligent retry detection for timeout errors

### 3. No Error Recovery ✅
**Problem**: Failed requests were logged but not retried, leading to data loss.

**Solution**:
- Added retry mechanism with configurable attempts (default: 3 retries)
- Implemented exponential backoff with jitter to avoid thundering herd
- Added specific handling for different error types

### 4. Poor Error Categorization ✅
**Problem**: All errors were treated the same way.

**Solution**:
- Added error categorization (timeout, connection, rate_limit, invalid_country_code, etc.)
- Implemented smart retry logic that only retries recoverable errors
- Enhanced logging with error types for better debugging

## New Features

### Configurable Retry Parameters
```php
// Default configuration
WebSearchCountryCodeLeadsJob::dispatch('week', ['bing', 'google']);

// Custom retry configuration
WebSearchCountryCodeLeadsJob::dispatch(
    'week',                 // time range
    ['bing', 'google'],     // search engines
    5,                      // max retries (default: 3)
    3,                      // base delay seconds (default: 2)
    90                      // request timeout seconds (default: 60)
);
```

### Intelligent Error Handling

**Retryable Errors**:
- Timeout errors (cURL error 28, operation timed out)
- Connection errors (cURL error 7, network failures)
- Server errors (HTTP 5xx)
- Rate limiting (HTTP 429, rate limit exceeded)

**Non-Retryable Errors**:
- Invalid country codes (HTTP 400 with specific message)
- Authentication errors (HTTP 401, 403)
- Bad requests (HTTP 400)

### Enhanced Logging
```json
{
  "search_engines": ["bing", "google"],
  "time_range": "week",
  "max_retries": 3,
  "base_delay": 2,
  "request_timeout": 60,
  "environment": "production",
  "test_mode": false
}
```

### Country Code Validation
- Filters out known invalid codes: `com`, `eu`, `store`, `website`, etc.
- Validates 2-letter alphabetic codes
- Logs filtered codes for debugging

## Exponential Backoff Algorithm

```
delay = baseDelay * (2 ^ (attempt - 1))
jitter = delay * 0.25 * random(-1, 1)
finalDelay = min(delay + jitter, 60)
```

**Example delays with baseDelay=2**:
- Attempt 1: ~2 seconds (±25% jitter)
- Attempt 2: ~4 seconds (±25% jitter)
- Attempt 3: ~8 seconds (±25% jitter)
- Maximum: 60 seconds

## Testing

### New Test Coverage
- Retry logic validation
- Error categorization
- Exponential backoff calculation
- Country code filtering
- Configuration parameter validation

### Running Tests
```bash
# Test retry functionality
./vendor/bin/pest tests/Unit/WebSearchCountryCodeLeadsJobRetryTest.php

# Test basic functionality
./vendor/bin/pest tests/Unit/WebSearchJobQuickTest.php

# Test all WebSearch functionality
./vendor/bin/pest tests/Unit/WebSearch*
```

## Usage Examples

### Basic Usage (Recommended)
```php
// Uses defaults: week timerange, 3 retries, 60s timeout
WebSearchCountryCodeLeadsJob::dispatch();
```

### High-Reliability Configuration
```php
// For problematic regions with frequent timeouts
WebSearchCountryCodeLeadsJob::dispatch(
    'week',                 // time range
    ['bing', 'google'],     // search engines
    5,                      // more retries
    3,                      // longer base delay
    120                     // longer timeout
);
```

### Fast Configuration
```php
// For testing or when speed is more important than reliability
WebSearchCountryCodeLeadsJob::dispatch(
    'day',                  // shorter time range
    ['google'],             // single engine
    1,                      // fewer retries
    1,                      // shorter delay
    30                      // shorter timeout
);
```

## Expected Improvements

1. **Reduced Failures**: Invalid country codes are now filtered out before API calls
2. **Better Recovery**: Timeout and connection errors are automatically retried
3. **Improved Reliability**: Exponential backoff prevents overwhelming APIs
4. **Better Debugging**: Enhanced logging with error categorization
5. **Configurable Behavior**: Adjust retry behavior based on requirements

## Monitoring

Watch for these log patterns to monitor improvements:

**Success after retry**:
```
[WebSearchCountryCodeLeadsJob] Search succeeded for bing country code cl on attempt 3
```

**Filtered invalid codes**:
```
[WebSearchCountryCodeLeadsJob] Filtered out invalid country codes for bing. {"invalid_codes":["com","eu"],"valid_codes_count":45}
```

**Retry attempts**:
```
[WebSearchCountryCodeLeadsJob] Retryable error on attempt 2 for bing country code cn. Retrying in 4 seconds.
```

The improvements should significantly reduce the errors seen in the original log file while maintaining the same functionality for collecting Shopify store leads.
