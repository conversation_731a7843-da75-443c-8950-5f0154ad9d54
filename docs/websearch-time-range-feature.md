# WebSearch Time Range Feature

## Overview

The `WebSearchTLDLeadsJob` (formerly `WebSearchCountryCodeLeadsJob`) now supports configurable time ranges for search results. You can search for results from the current day, last 7 days, or last 30 days, with newest results appearing first.

## Available Time Ranges

- **`week`** - Last 7 days (newest first) ⭐ **DEFAULT**
- **`day`** - Current day only
- **`month`** - Last 30 days (newest first)

## How to Use

### 1. Using Laravel Tinker

```php
// Start tinker
php artisan tinker

// Last 7 days (newest first) - DEFAULT
App\Jobs\WebSearchTLDLeadsJob::dispatch();

// Or explicitly specify week
App\Jobs\WebSearchTLDLeadsJob::dispatch('week', ['bing', 'google']);

// Current day only
App\Jobs\WebSearchTLDLeadsJob::dispatch('day', ['bing', 'google']);

// Last 30 days (newest first)
App\Jobs\WebSearchTLDLeadsJob::dispatch('month', ['bing', 'google']);

// Only Google with specific time range
App\Jobs\WebSearchTLDLeadsJob::dispatch('week', ['google']);

// Only Google with default time range (week)
App\Jobs\WebSearchTLDLeadsJob::dispatch();
```

### 2. In Your Code

```php
use App\Jobs\WebSearchTLDLeadsJob;

// Last 7 days (newest first) - DEFAULT
WebSearchTLDLeadsJob::dispatch();

// Or explicitly specify week
WebSearchTLDLeadsJob::dispatch('week', ['bing', 'google']);

// Current day only
WebSearchTLDLeadsJob::dispatch('day', ['bing', 'google']);

// Last 30 days (newest first)
WebSearchTLDLeadsJob::dispatch('month', ['bing', 'google']);

// Only specific time range (uses default engines)
WebSearchTLDLeadsJob::dispatch('week');
```

## Technical Details

### Search Engine Parameters

The job automatically configures the correct parameters for each search engine:

**Google:**
- `day`: `tbs=qdr:d` (current day)
- `week`: `tbs=qdr:w` (last 7 days)
- `month`: `tbs=qdr:m` (last 30 days)

**Bing:**
- `day`: `time=day` (current day)
- `week`: `time=week` (last 7 days)
- `month`: `time=month` (last 30 days)

### Result Ordering

Search engines typically return results with newest first by default, which is exactly what you want for finding recent Shopify stores.

### Logging

The job logs include the time range setting:

```
[WebSearchTLDLeadsJob] Starting job. {
    "search_engines": ["bing", "google"],
    "time_range": "week",
    "environment": "local",
    "test_mode": true
}
```

## Queue Processing

Don't forget to run the queue worker to process the jobs:

```bash
php artisan queue:work
```

## Examples for Your Use Case

Since you want to get results from the last 7 days with newest first, you can now use these simple patterns:

```php
// Uses default week setting
WebSearchTLDLeadsJob::dispatch();

// Or specify time range only (uses default engines)
WebSearchTLDLeadsJob::dispatch('week');

// Or specify both time range and engines
WebSearchTLDLeadsJob::dispatch('week', ['bing', 'google']);
```

This will:
1. Search for Shopify stores in the last 7 days (default)
2. Return results with newest first
3. Cover more ground than just current day
4. Still be recent enough to be relevant

## Error Handling

If you provide an invalid time range, the job will:
1. Log a warning
2. Default to 'week' time range (last 7 days)
3. Continue processing

Valid time ranges are: `day`, `week`, `month`

## Performance Considerations

- **`week`** provides a good balance between coverage and relevance (now the default)
- **`month`** may return more results but could include older, less relevant stores
- **`day`** is fastest but may miss stores that were indexed yesterday

For your use case of finding new Shopify stores, **`week`** is now the default setting, so you don't need to specify it explicitly.
