<?php

namespace App\Jobs;

use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;
use App\Helpers\WebSearch\BrightDataSerpHelper;
use App\Helpers\WebSearch\SerpApiHelper;
use App\Helpers\WebSearch\SerpStackHelper;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class WebSearchCountryCodeLeadsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The search engines to use.
     */
    protected array $searchEngines;

    /**
     * The time range for search results.
     */
    protected string $timeRange;

    // No longer using countryCodes property - each engine uses its own country codes

    /**
     * Create a new job instance.
     *
     * @param  string  $timeRange  The time range for search results (supported: 'day', 'week', 'month')
     *                             - 'day': Current day only
     *                             - 'week': Last 7 days (newest first) [DEFAULT]
     *                             - 'month': Last 30 days (newest first)
     * @param  array  $searchEngines  The search engines to use (supported: 'bing', 'google')
     *                                Note:
     *                                - 'bing' can use either SerpStackHelper or BrightDataSerpHelper (if configured)
     *                                - 'google' can use either SerpApiHelper or BrightDataSerpHelper (if configured)
     *                                All search engines are configured to return 100 results with newest results first.
     */
    public function __construct(
        string $timeRange = 'week',
        array $searchEngines = ['bing', 'google']
    ) {
        $this->searchEngines = $searchEngines;
        $this->timeRange = $this->validateTimeRange($timeRange);
    }

    /**
     * Validate and normalize the time range parameter.
     *
     * @param  string  $timeRange  The time range to validate
     * @return string The validated time range
     */
    protected function validateTimeRange(string $timeRange): string
    {
        $validRanges = ['day', 'week', 'month'];
        $normalizedRange = strtolower(trim($timeRange));

        if (! in_array($normalizedRange, $validRanges)) {
            Log::warning('[WebSearchCountryCodeLeadsJob] Invalid time range provided, defaulting to "week".', [
                'provided_range' => $timeRange,
                'valid_ranges' => $validRanges,
            ]);

            return 'week';
        }

        return $normalizedRange;
    }

    /**
     * Get time filter parameters for different search engines.
     *
     * @param  string  $engine  The search engine name
     * @return array The time filter parameters
     */
    protected function getTimeFilterParams(string $engine): array
    {
        switch ($engine) {
            case 'google':
                // Google uses 'tbs' parameter for time filtering
                switch ($this->timeRange) {
                    case 'week':
                        return ['tbs' => 'qdr:w']; // Last week (7 days)
                    case 'month':
                        return ['tbs' => 'qdr:m']; // Last month (30 days)
                    case 'day':
                    default:
                        return ['tbs' => 'qdr:d']; // Current day
                }

            case 'bing':
                // Bing uses 'time' parameter for time filtering
                switch ($this->timeRange) {
                    case 'week':
                        return ['time' => 'week']; // Last week (7 days)
                    case 'month':
                        return ['time' => 'month']; // Last month (30 days)
                    case 'day':
                    default:
                        return ['time' => 'day']; // Current day
                }

            default:
                return [];
        }
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $environment = app()->environment();
        Log::info('[WebSearchCountryCodeLeadsJob] Starting job.', [
            'search_engines' => $this->searchEngines,
            'time_range' => $this->timeRange,
            'environment' => $environment,
            'test_mode' => ($environment !== 'production'),
        ]);

        $allParsedLeads = [];
        $totalLeadsCollected = 0;

        $searchHelpers = $this->initializeSearchHelpers();
        if (empty($searchHelpers)) {
            Log::error('[WebSearchCountryCodeLeadsJob] No search helpers could be initialized. Aborting job.');

            return;
        }

        foreach ($searchHelpers as $engineName => $helperConfig) {
            Log::info("[WebSearchCountryCodeLeadsJob] Processing {$engineName} search with ".count($helperConfig['country_codes']).' country codes');

            foreach ($helperConfig['country_codes'] as $countryCode) {
                $query = 'site:.'.$countryCode.' inurl:"/collections/all/" -site:shopify.com -site:stackoverflow.com -site:help.shopify.com -site:accounts.shopify.com -site:shopify.dev -site:myshopify.com';

                Log::info("[WebSearchCountryCodeLeadsJob] Processing {$engineName} search for country code: {$countryCode} with query: {$query}");

                $params = $this->prepareSearchParams($helperConfig['search_params'], $countryCode);

                try {
                    $results = match ($helperConfig['type']) {
                        'brightdata' => $helperConfig['class']->search($engineName, $query, strtoupper($countryCode), $params),
                        'serpstack' => $helperConfig['class']->search($query, $params),
                        'serpapi' => $helperConfig['class']->search('google', $query, strtoupper($countryCode), $params),
                        default => null,
                    };

                    if (! empty($results)) {
                        $parsedResults = $this->processSearchResults($results, $engineName);

                        if (! empty($parsedResults)) {
                            $allParsedLeads = array_merge($allParsedLeads, $parsedResults);
                            $totalLeadsCollected += count($parsedResults);

                            Log::info("[WebSearchCountryCodeLeadsJob] Collected {$totalLeadsCollected} leads so far from {$engineName} for country code: {$countryCode}");
                        }
                    }
                } catch (Exception $e) {
                    Log::error("[WebSearchCountryCodeLeadsJob] Error performing search with {$engineName} for country code {$countryCode}: ".$e->getMessage());
                }

                sleep(1);
            }
        }

        if (! empty($allParsedLeads)) {
            Log::info('[WebSearchCountryCodeLeadsJob] Passing '.count($allParsedLeads).' collected leads to batch processor.');
            (new LeadBatchProcessorHelper)->processLeads($allParsedLeads);
        } else {
            Log::info('[WebSearchCountryCodeLeadsJob] No leads collected to process.');
        }

        Log::info("[WebSearchCountryCodeLeadsJob] Job finished. Total leads prepared for batching: {$totalLeadsCollected}");
    }

    /**
     * Initialize the search helpers for each enabled search engine.
     * This method also configures country codes and search parameters for each engine.
     *
     * @return array The initialized search helpers with configuration
     *
     * @throws Exception
     */
    protected function initializeSearchHelpers(): array
    {
        $helpers = [];

        $brightDataConfig = [
            'api_key' => config('crawler.brightdata_api_key'),
            'customer_id' => config('crawler.brightdata_customer_id'),
            'zone_id' => config('crawler.brightdata_zone_id'),
        ];

        $brightDataConfigured = ! empty($brightDataConfig['api_key']) &&
            ! empty($brightDataConfig['customer_id']) &&
            ! empty($brightDataConfig['zone_id']);

        $brightDataHelper = null;
        if ($brightDataConfigured) {
            $brightDataHelper = new BrightDataSerpHelper(
                $brightDataConfig['api_key'],
                $brightDataConfig['customer_id'],
                $brightDataConfig['zone_id']
            );
            Log::info('[WebSearchCountryCodeLeadsJob] Initialized BrightDataSerpHelper');
        }

        $commonTlds = ['com', 'eu', 'nu', 'store', 'net', 'online', 'org', 'info', 'biz', 'site', 'website', 'tech', 'app', 'blog'];

        foreach ($this->searchEngines as $engine) {
            try {
                $engineName = strtolower($engine);
                $helperConfig = $this->createHelperConfig($engineName, $brightDataConfigured, $brightDataHelper, $commonTlds);

                if (! $helperConfig) {
                    continue;
                }

                $helpers[$engineName] = $helperConfig;

            } catch (Exception $e) {
                Log::error("[WebSearchCountryCodeLeadsJob] Error initializing search helper for {$engine}: ".$e->getMessage());
            }
        }

        return $helpers;
    }

    /**
     * Create helper configuration for a specific search engine.
     *
     * @param  string  $engineName  The search engine name
     * @param  bool  $brightDataConfigured  Whether BrightData is configured
     * @param  BrightDataSerpHelper|null  $brightDataHelper  The BrightData helper instance
     * @param  array  $commonTlds  Common TLDs to include
     * @return array|null The helper configuration or null if not available
     */
    protected function createHelperConfig(string $engineName, bool $brightDataConfigured, ?BrightDataSerpHelper $brightDataHelper, array $commonTlds): ?array
    {
        switch ($engineName) {
            case 'bing':
                if ($brightDataConfigured) {
                    return $this->createBingBrightDataConfig($brightDataHelper, $commonTlds);
                } elseif ($serpStackApiKey = config('crawler.serpstack_api_key')) {
                    return $this->createBingSerpStackConfig($serpStackApiKey, $commonTlds);
                } else {
                    Log::warning('[WebSearchCountryCodeLeadsJob] No API keys configured for Bing search. Skipping.');

                    return null;
                }

            case 'google':
                if ($brightDataConfigured) {
                    return $this->createGoogleBrightDataConfig($brightDataHelper, $commonTlds);
                } elseif ($serpApiKey = config('crawler.serpapi_api_key')) {
                    return $this->createGoogleSerpApiConfig($serpApiKey, $commonTlds);
                } else {
                    Log::warning('[WebSearchCountryCodeLeadsJob] No API keys configured for Google search. Skipping.');

                    return null;
                }

            default:
                Log::warning("[WebSearchCountryCodeLeadsJob] Unsupported search engine: {$engineName}. Skipping.");

                return null;
        }
    }

    /**
     * Create Bing BrightData configuration.
     */
    protected function createBingBrightDataConfig(BrightDataSerpHelper $brightDataHelper, array $commonTlds): array
    {
        $countryCodes = $this->prepareCountryCodes(
            array_map('strtolower', BrightDataSerpHelper::SUPPORTED_COUNTRY_CODES),
            $commonTlds,
            'bing'
        );

        $searchParams = array_merge([
            'engine' => 'bing',
            'country_code' => '{COUNTRY_CODE}',
            'count' => 100,
        ], $this->getTimeFilterParams('bing'));

        Log::info('[WebSearchCountryCodeLeadsJob] Using BrightDataSerpHelper for Bing');

        return [
            'class' => $brightDataHelper,
            'type' => 'brightdata',
            'engine' => 'bing',
            'country_codes' => $countryCodes,
            'search_params' => $searchParams,
        ];
    }

    /**
     * Create Bing SerpStack configuration.
     */
    protected function createBingSerpStackConfig(string $serpStackApiKey, array $commonTlds): array
    {
        $countryCodes = $this->prepareCountryCodes(
            array_map('strtolower', SerpStackHelper::SUPPORTED_COUNTRY_CODES),
            $commonTlds,
            'bing'
        );

        $searchParams = array_merge([
            'engine' => 'bing',
            'country_code' => '{COUNTRY_CODE}',
            'num' => 100,
        ], $this->getTimeFilterParams('bing'));

        Log::info('[WebSearchCountryCodeLeadsJob] Using SerpStackHelper for Bing');

        return [
            'class' => new SerpStackHelper($serpStackApiKey, true),
            'type' => 'serpstack',
            'engine' => 'bing',
            'country_codes' => $countryCodes,
            'search_params' => $searchParams,
        ];
    }

    /**
     * Create Google BrightData configuration.
     */
    protected function createGoogleBrightDataConfig(BrightDataSerpHelper $brightDataHelper, array $commonTlds): array
    {
        $countryCodes = $this->prepareCountryCodes(
            array_map('strtolower', BrightDataSerpHelper::SUPPORTED_COUNTRY_CODES),
            $commonTlds,
            'google'
        );

        $searchParams = array_merge([
            'gl' => '{COUNTRY_CODE}',
            'num' => 100,
        ], $this->getTimeFilterParams('google'));

        Log::info('[WebSearchCountryCodeLeadsJob] Using BrightDataSerpHelper for Google');

        return [
            'class' => $brightDataHelper,
            'type' => 'brightdata',
            'engine' => 'google',
            'country_codes' => $countryCodes,
            'search_params' => $searchParams,
        ];
    }

    /**
     * Create Google SerpApi configuration.
     */
    protected function createGoogleSerpApiConfig(string $serpApiKey, array $commonTlds): array
    {
        $countryCodes = $this->prepareCountryCodes(
            array_map('strtolower', SerpApiHelper::SUPPORTED_COUNTRY_CODES),
            $commonTlds,
            'google'
        );

        $searchParams = array_merge([
            'gl' => '{COUNTRY_CODE}',
            'country_code' => '{COUNTRY_CODE}',
            'num' => 100,
        ], $this->getTimeFilterParams('google'));

        Log::info('[WebSearchCountryCodeLeadsJob] Using SerpApiHelper for Google');

        return [
            'class' => new SerpApiHelper($serpApiKey),
            'type' => 'serpapi',
            'engine' => 'google',
            'country_codes' => $countryCodes,
            'search_params' => $searchParams,
        ];
    }

    /**
     * Prepare country codes by merging with common TLDs and applying environment filters.
     */
    protected function prepareCountryCodes(array $supportedCountryCodes, array $commonTlds, string $engineName): array
    {
        $countryCodes = array_unique(array_merge($supportedCountryCodes, $commonTlds));
        sort($countryCodes);

        if (app()->environment() !== 'production') {
            Log::info("[WebSearchCountryCodeLeadsJob] Non-production environment detected. Using only one country code for {$engineName}.");

            return ['dk'];
        }

        return $countryCodes;
    }

    /**
     * Prepare search parameters by replacing placeholders with actual values.
     *
     * @param  array  $params  The search parameters with placeholders
     * @param  string  $countryCode  The country code to use
     * @return array The prepared search parameters
     */
    protected function prepareSearchParams(array $params, string $countryCode): array
    {
        return array_map(
            fn ($value) => is_string($value) && $value === '{COUNTRY_CODE}'
                ? strtolower($countryCode)
                : $value,
            $params
        );
    }

    /**
     * Process the search results and extract domain information.
     *
     * @param  array  $results  The search results
     * @param  string  $engineName  The name of the search engine
     * @return array The processed results with domain information
     */
    protected function processSearchResults(array $results, string $engineName): array
    {
        try {
            $organicResults = $this->extractOrganicResults($results, $engineName);

            if (empty($organicResults)) {
                Log::info("[WebSearchCountryCodeLeadsJob] No organic results found for {$engineName}.");

                return [];
            }

            $processedResults = array_filter(
                array_map(function ($result) use ($engineName) {
                    $url = $this->extractUrlFromResult($result, $engineName);

                    return empty(trim($url)) ? null : DomainParserHelper::getProcessedDomainInformation($url);
                }, $organicResults)
            );

            Log::info('[WebSearchCountryCodeLeadsJob] Processed '.count($processedResults)." results from {$engineName}.");

            return $processedResults;

        } catch (Exception $e) {
            Log::error("[WebSearchCountryCodeLeadsJob] Error processing search results from {$engineName}: ".$e->getMessage());

            return [];
        }
    }

    /**
     * Extract organic results from the search results based on the search engine.
     *
     * @param  array  $results  The search results
     * @param  string  $engineName  The name of the search engine
     * @return array The organic results
     */
    protected function extractOrganicResults(array $results, string $engineName): array
    {
        Log::debug("[WebSearchCountryCodeLeadsJob] Extracting organic results for {$engineName}.", [
            'engine' => $engineName,
            'result_keys' => array_keys($results),
            'result_structure' => $this->getResultStructureDebug($results),
        ]);

        $organicResults = match ($engineName) {
            'bing', 'google' => $results['organic'] ?? [],
            default => [],
        };

        Log::debug("[WebSearchCountryCodeLeadsJob] Extracted {$engineName} organic results.", [
            'engine' => $engineName,
            'organic_results_count' => count($organicResults),
            'first_result_keys' => ! empty($organicResults) ? array_keys($organicResults[0] ?? []) : [],
        ]);

        return $organicResults;
    }

    /**
     * Extract the URL from a search result based on the search engine.
     *
     * @param  array  $result  The search result
     * @param  string  $engineName  The name of the search engine
     * @return string The extracted URL
     */
    protected function extractUrlFromResult(array $result, string $engineName): string
    {
        Log::debug("[WebSearchCountryCodeLeadsJob] Extracting URL from {$engineName} result.", [
            'engine' => $engineName,
            'result_keys' => array_keys($result),
            'url_field_link' => $result['link'] ?? 'not_found',
        ]);

        return match ($engineName) {
            'bing', 'google' => $result['link'] ?? '',
            default => '',
        };
    }

    /**
     * Get a debug representation of the result structure.
     *
     * @param  array  $results  The search results
     * @return array Debug information about the structure
     */
    protected function getResultStructureDebug(array $results): array
    {
        return array_map(function ($value, $key) {
            if (is_array($value)) {
                $debug = [
                    'type' => 'array',
                    'count' => count($value),
                    'keys' => ! empty($value) ? array_keys($value) : [],
                ];

                if (isset($value[0]) && is_array($value[0])) {
                    $debug['first_element_keys'] = array_keys($value[0]);
                }

                return $debug;
            }

            return [
                'type' => gettype($value),
                'value' => is_string($value) ? substr($value, 0, 100) : $value,
            ];
        }, $results, array_keys($results));
    }
}
