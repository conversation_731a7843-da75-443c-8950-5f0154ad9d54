<?php

use App\Jobs\WebSearchTLDLeadsJob;
use Illuminate\Support\Facades\Queue;

describe('WebSearchTLDLeadsJob Integration', function () {

    it('can be queued with default parameters', function () {
        Queue::fake();

        WebSearchTLDLeadsJob::dispatch();

        Queue::assertPushed(WebSearchTLDLeadsJob::class, 1);
    });

    it('can be queued with custom time range', function () {
        Queue::fake();

        WebSearchTLDLeadsJob::dispatch('day');

        Queue::assertPushed(WebSearchTLDLeadsJob::class, 1);
    });

    it('can be queued with custom engines', function () {
        Queue::fake();

        WebSearchTLDLeadsJob::dispatch('week', ['google']);

        Queue::assertPushed(WebSearchTLDLeadsJob::class, 1);
    });

    it('supports the requested dispatch patterns', function () {
        Queue::fake();

        // Test the patterns you requested
        WebSearchTLDLeadsJob::dispatch('week', ['bing', 'google']);
        WebSearchTLDLeadsJob::dispatch('week');

        Queue::assertPushed(WebSearchTLDLeadsJob::class, 2);
    });

});
