<?php

use App\Jobs\WebSearchTLDLeadsJob;

it('can create WebSearchTLDLeadsJob with default parameters', function () {
    $job = new WebSearchTLDLeadsJob;

    expect($job)->toBeInstanceOf(WebSearchTLDLeadsJob::class);
});

it('can create WebSearchTLDLeadsJob with time range', function () {
    $job = new WebSearchTLDLeadsJob('day');

    expect($job)->toBeInstanceOf(WebSearchTLDLeadsJob::class);
});

it('can create WebSearchTLDLeadsJob with time range and engines', function () {
    $job = new WebSearchTLDLeadsJob('week', ['google']);

    expect($job)->toBeInstanceOf(WebSearchTLDLeadsJob::class);
});

it('can dispatch WebSearchTLDLeadsJob', function () {
    expect(function () {
        WebSearchTLDLeadsJob::dispatch();
    })->not->toThrow(Exception::class);
});

it('can dispatch WebSearchTLDLeadsJob with time range', function () {
    expect(function () {
        WebSearchTLDLeadsJob::dispatch('week');
    })->not->toThrow(Exception::class);
});

it('can dispatch WebSearchTLDLeadsJob with time range and engines', function () {
    expect(function () {
        WebSearchTLDLeadsJob::dispatch('week', ['google']);
    })->not->toThrow(Exception::class);
});
